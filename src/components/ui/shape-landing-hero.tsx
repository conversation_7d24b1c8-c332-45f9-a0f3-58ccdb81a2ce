"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import Link from "next/link";

function FloatingShapes({ position }: { position: number }) {
  const shapes = Array.from({ length: 24 }, (_, i) => ({
    id: i,
    // Create curved path shapes similar to the background paths
    d: `M${100 + i * 15 * position} ${50 + i * 8}Q${200 + i * 12 * position} ${150 + i * 10} ${300 + i * 18 * position} ${100 + i * 6}T${500 + i * 20 * position} ${200 + i * 12}`,
    color: `rgba(99, 102, 241, ${0.1 + i * 0.02})`, // Indigo color scheme
    width: 0.8 + i * 0.04,
    delay: i * 0.1,
  }));

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg
        className="w-full h-full text-indigo-500 dark:text-indigo-400"
        viewBox="0 0 800 600"
        fill="none"
      >
        <title>Floating Shapes</title>
        {shapes.map((shape) => (
          <motion.path
            key={shape.id}
            d={shape.d}
            stroke="currentColor"
            strokeWidth={shape.width}
            strokeOpacity={0.15 + shape.id * 0.02}
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{
              pathLength: [0, 1, 0.8],
              opacity: [0.2, 0.6, 0.3],
              strokeDashoffset: [0, -100, -200],
            }}
            transition={{
              duration: 15 + Math.random() * 8,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
              delay: shape.delay,
            }}
          />
        ))}
      </svg>
    </div>
  );
}

export function ShapeLandingHero({
  title = "Shape Hero",
  subtitle = "Beautiful animated shapes",
}: {
  title?: string;
  subtitle?: string;
}) {
  const words = title.split(" ");

  // Animation variants for the download button
  const pulseAnimation = {
    initial: { scale: 1 },
    animate: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 3,
        repeat: Infinity,
        repeatType: "reverse" as const,
      },
    },
  };

  return (
    <div className="relative min-h-screen w-full flex flex-col bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-slate-950 dark:via-neutral-950 dark:to-indigo-950">
      {/* Background and Content */}
      <div className="flex-1 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <FloatingShapes position={1} />
          <FloatingShapes position={-0.5} />
        </div>

        <div className="relative z-10 container mx-auto px-4 md:px-6 text-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="max-w-4xl mx-auto"
          >
            <div className="mb-6 flex justify-center items-center space-x-3">
              <span className="inline-block px-3 py-1 text-xs font-medium tracking-wider uppercase bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 rounded-md">
                SHAPES
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                v1.0.0
              </span>
            </div>

            <h1 className="text-6xl xs:text-7xl sm:text-8xl md:text-9xl lg:text-[10rem] font-bold mb-4 sm:mb-8 md:mb-10 tracking-tighter">
              {words.map((word, wordIndex) => (
                <span key={wordIndex} className="inline-block mr-4 last:mr-0">
                  {word.split("").map((letter, letterIndex) => (
                    <motion.span
                      key={`${wordIndex}-${letterIndex}`}
                      initial={{ y: 100, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{
                        delay: wordIndex * 0.1 + letterIndex * 0.03,
                        type: "spring",
                        stiffness: 150,
                        damping: 25,
                      }}
                      className="inline-block text-transparent bg-clip-text
                                        bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800
                                        dark:from-indigo-400 dark:via-purple-400 dark:to-indigo-300"
                    >
                      {letter}
                    </motion.span>
                  ))}
                </span>
              ))}
            </h1>

            <div className="flex flex-col xs:flex-row justify-center gap-2 xs:gap-4 mt-6 sm:mt-8">
              <motion.div
                variants={pulseAnimation}
                initial="initial"
                animate="animate"
              >
                <Link href="/download">
                  <Button
                    size="default"
                    className="group gap-2 text-sm px-4 py-2 h-auto w-full xs:w-auto hover:scale-105 transition-transform bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Download className="h-4 w-4 group-hover:translate-y-0.5 transition-transform" />
                    Get Started
                  </Button>
                </Link>
              </motion.div>

              <Link href="/about">
                <Button
                  variant="outline"
                  size="default"
                  className="group gap-2 text-sm px-4 py-2 h-auto border-indigo-300 dark:border-indigo-700 hover:scale-105 transition-transform w-full xs:w-auto"
                >
                  Learn More
                </Button>
              </Link>
            </div>

            <div className="mt-8 sm:mt-10 text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto px-2 xs:px-4">
              <p>
                {subtitle || "Experience beautiful animated shapes and modern design patterns that bring your interface to life."}
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
