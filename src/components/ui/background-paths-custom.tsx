"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import Link from "next/link";
import { Navbar1 } from "@/components/ui/navbar-1";

function FloatingPaths({ position }: { position: number }) {
  const paths = Array.from({ length: 36 }, (_, i) => ({
    id: i,
    d: `M-${380 - i * 5 * position} -${189 + i * 6}C-${
      380 - i * 5 * position
    } -${189 + i * 6} -${312 - i * 5 * position} ${216 - i * 6} ${
      152 - i * 5 * position
    } ${343 - i * 6}C${616 - i * 5 * position} ${470 - i * 6} ${
      684 - i * 5 * position
    } ${875 - i * 6} ${684 - i * 5 * position} ${875 - i * 6}`,
    color: `rgba(15,23,42,${0.1 + i * 0.03})`,
    width: 0.5 + i * 0.03,
  }));

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg
        className="w-full h-full text-slate-950 dark:text-white"
        viewBox="0 0 696 316"
        fill="none"
      >
        <title>Background Paths</title>
        {paths.map((path) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke="currentColor"
            strokeWidth={path.width}
            strokeOpacity={0.1 + path.id * 0.03}
            initial={{ pathLength: 0.3, opacity: 0.6 }}
            animate={{
              pathLength: 1,
              opacity: [0.3, 0.6, 0.3],
              pathOffset: [0, 1, 0],
            }}
            transition={{
              duration: 20 + Math.random() * 10,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        ))}
      </svg>
    </div>
  );
}

export function CustomBackgroundPaths({
  title = "Sidekick",
}: {
  title?: string;
}) {
  const words = title.split(" ");

  // Animation variants for the download button
  const pulseAnimation = {
    initial: { scale: 1 },
    animate: {
      scale: [1, 1.03, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "reverse" as const,
      },
    },
  };

  return (
    <div className="relative min-h-screen w-full flex flex-col bg-white dark:bg-neutral-950">
      {/* Navbar */}
      <div className="relative z-20">
        <Navbar1 />
      </div>

      {/* Background and Content */}
      <div className="flex-1 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <FloatingPaths position={1} />
          <FloatingPaths position={-1} />
        </div>

        <div className="relative z-10 container mx-auto px-4 md:px-6 text-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className="max-w-4xl mx-auto"
          >
            <div className="mb-6 flex justify-center items-center space-x-3">
              <span className="inline-block px-3 py-1 text-xs font-medium tracking-wider uppercase bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-md">
                BETA
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                v0.1.0
              </span>
            </div>

            <h1 className="text-6xl xs:text-7xl sm:text-8xl md:text-9xl lg:text-[10rem] font-bold mb-4 sm:mb-8 md:mb-10 tracking-tighter">
              {words.map((word, wordIndex) => (
                <span key={wordIndex} className="inline-block mr-4 last:mr-0">
                  {word.split("").map((letter, letterIndex) => (
                    <motion.span
                      key={`${wordIndex}-${letterIndex}`}
                      initial={{ y: 100, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{
                        delay: wordIndex * 0.1 + letterIndex * 0.03,
                        type: "spring",
                        stiffness: 150,
                        damping: 25,
                      }}
                      className="inline-block text-transparent bg-clip-text 
                                        bg-gradient-to-r from-neutral-900 to-neutral-700/80 
                                        dark:from-white dark:to-white/80"
                    >
                      {letter}
                    </motion.span>
                  ))}
                </span>
              ))}
            </h1>

            <div className="flex flex-col xs:flex-row justify-center gap-2 xs:gap-4 mt-6 sm:mt-8">
              <motion.div
                variants={pulseAnimation}
                initial="initial"
                animate="animate"
              >
                <Link href="/download">
                  <Button
                    size="default"
                    className="group gap-2 text-sm px-4 py-2 h-auto w-full xs:w-auto hover:scale-105 transition-transform"
                  >
                    <Download className="h-4 w-4 group-hover:translate-y-0.5 transition-transform" />
                    Download Sidekick
                  </Button>
                </Link>
              </motion.div>

              <Link href="/changelog">
                <Button
                  variant="outline"
                  size="default"
                  className="group gap-2 text-sm px-4 py-2 h-auto border-gray-300 dark:border-gray-700 hover:scale-105 transition-transform w-full xs:w-auto"
                >
                  Changelog
                </Button>
              </Link>
            </div>

            <div className="mt-8 sm:mt-10 text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto px-2 xs:px-4">
              <p>
                AI coding assistant that helps developers write, edit, and
                improve code directly on their computer.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
