import React from "react";
import Link from "next/link";

export function Footer() {
  return (
    <footer className="w-full py-4 text-center border-t border-neutral-800 dark:border-neutral-800 bg-neutral-950">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="text-sm text-neutral-400">
            <span className="font-semibold text-neutral-300">Sidekick</span> by
            Yugin Tech • Multiple AI providers supported
          </div>

          <div className="flex items-center justify-center md:justify-end gap-6 text-sm">
            <Link
              href="/download"
              className="text-neutral-400 hover:text-white transition-colors"
            >
              Download
            </Link>
            <Link
              href="/changelog"
              className="text-neutral-400 hover:text-white transition-colors"
            >
              Changelog
            </Link>
            <a
              href="mailto:<EMAIL>"
              className="text-neutral-400 hover:text-blue-400 transition-colors"
            >
              Contact
            </a>
            <span className="text-neutral-400">
              Made with <span className="text-red-500">❤️</span>
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
