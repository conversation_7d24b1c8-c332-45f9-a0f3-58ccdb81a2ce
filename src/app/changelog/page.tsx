import { Timeline } from "@/components/ui/timeline";
import { Footer } from "@/components/ui/footer";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Navbar1 } from "@/components/ui/navbar-1";

export const metadata = {
  title: "Changelog | Sidekick by Yugin Tech",
  description:
    "Track the development progress and updates of Side<PERSON>, the AI coding assistant by Yugin Tech.",
};

export default function ChangelogPage() {
  const timelineData = [
    {
      title: "v0.1.0",
      content: (
        <div>
          <div className="mb-2">
            <div className="flex items-center gap-3 mb-1">
              <h4 className="text-2xl font-bold text-black dark:text-white">
                Initial Beta Release
              </h4>
              <span className="px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                BETA
              </span>
            </div>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              Released on July 1, 2025
            </p>
          </div>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6 text-lg">
            First public beta release of Side<PERSON>, our AI coding assistant.
          </p>

          <div className="space-y-3">
            <ul className="space-y-3 list-disc list-outside ml-5 text-neutral-700 dark:text-neutral-300">
              <li>
                <span className="font-semibold">Command Palette</span> - Quick
                access to features and commands with keyboard shortcuts
              </li>

              <li>
                <span className="font-semibold">Customizable Layout</span> -
                Adjustable sidebar positioning and resizable panels
              </li>

              <li>
                <span className="font-semibold">Custom Prompt Templates</span> -
                Create and manage reusable prompt templates
              </li>

              <li>
                <span className="font-semibold">
                  Model Context Protocol (MCP)
                </span>{" "}
                - Extend AI capabilities with external tools
              </li>

              <li>
                <span className="font-semibold">Multi-AI Provider Support</span>{" "}
                - Connect with OpenAI, Anthropic Claude, Google Gemini, and
                Ollama (local models)
              </li>

              <li>
                <span className="font-semibold">Bring Your Own API Key</span> -
                Use your own API keys for complete privacy and control
              </li>

              <li>
                <span className="font-semibold">Local-First Architecture</span>{" "}
                - Everything stays on your system, no data leaves your machine
              </li>

              <li>
                <span className="font-semibold">Integrated Code Editor</span> -
                Monaco-based editor with syntax highlighting and IntelliSense
              </li>

              <li>
                <span className="font-semibold">AI Chat Interface</span> -
                Interactive sidebar for conversing with AI models about your
                code
              </li>

              <li>
                <span className="font-semibold">File System Integration</span> -
                Built-in file explorer for browsing and managing project files
              </li>

              <li>
                <span className="font-semibold">AI Code Capabilities</span>:
                <ul className="list-disc list-outside ml-5 mt-2 space-y-1 text-sm">
                  <li>Read and analyze existing code</li>
                  <li>Write new code and functions</li>
                  <li>Edit and refactor code intelligently</li>
                  <li>Scan folders and project structure</li>
                  <li>Search files with grep patterns</li>
                  <li>Understand context across multiple files</li>
                </ul>
              </li>

              <li>
                <span className="font-semibold">Terminal Integration</span> -
                Terminal console for running commands without leaving the app
              </li>

              <li>
                <span className="font-semibold">Submit Feedback</span> - Add a
                bug report or feature request
              </li>

              <li>
                <span className="font-semibold">Preview Beta</span> - Preview
                web applications directly within Sidekick
              </li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-950 flex flex-col">
      <div className="absolute top-4 right-4 z-20">
        <ThemeToggle />
      </div>

      <Navbar1 />

      <main className="flex-grow">
        <Timeline data={timelineData} />
      </main>

      <Footer />
    </div>
  );
}
