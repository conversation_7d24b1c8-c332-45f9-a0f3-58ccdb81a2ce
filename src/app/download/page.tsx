import { Footer } from "@/components/ui/footer";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Navbar1 } from "@/components/ui/navbar-1";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Apple, Monitor, Laptop, Download, Clock } from "lucide-react";

export const metadata = {
  title: "Download Sidekick | AI Coding Assistant by Yugin Tech",
  description:
    "Download Sidekick, the intelligent AI coding assistant by Yugin Tech. Available for macOS, Windows, and Linux.",
};

export default function DownloadPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-neutral-950 flex flex-col">
      <div className="absolute top-4 right-4 z-20">
        <ThemeToggle />
      </div>

      <Navbar1 />

      <main className="flex-grow">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="text-5xl md:text-7xl font-bold mb-6 text-black dark:text-white">
                Download Sidekick
              </h1>
              <p className="text-xl text-black dark:text-white max-w-2xl mx-auto">
                The intelligent AI coding assistant that understands your codebase and accelerates your development workflow.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* macOS Download - Available */}
              <Card className="relative group transition-all duration-300 hover:scale-105 border-2 border-black dark:border-white">
                <CardHeader className="text-center pt-8">
                  <div className="w-20 h-20 mx-auto mb-4 border-2 border-black dark:border-white rounded-2xl flex items-center justify-center">
                    <Apple className="w-10 h-10 text-black dark:text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-black dark:text-white">macOS</CardTitle>
                  <CardDescription className="text-base text-black dark:text-white">
                    Apple Silicon Native
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center space-y-4 pb-8">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-black dark:text-white">
                      macOS 11.0 or later
                    </p>
                    <p className="text-sm text-black dark:text-white opacity-70">
                      Optimized for M1/M2/M3
                    </p>
                  </div>
                  <a 
                    href="https://github.com/yugintech/sidekick/releases/latest/download/Sidekick-macOS.dmg"
                    download="Sidekick-macOS.dmg"
                    className="w-full block"
                  >
                    <Button size="lg" className="w-full bg-black dark:bg-white text-white dark:text-black hover:bg-black/80 dark:hover:bg-white/80 border-0">
                      <Download className="w-4 h-4 mr-2" />
                      Download .dmg
                    </Button>
                  </a>
                </CardContent>
              </Card>

              {/* Windows Download - Coming Soon */}
              <Card className="relative border border-black dark:border-white opacity-30">
                <CardHeader className="text-center pt-8">
                  <div className="w-20 h-20 mx-auto mb-4 border border-black dark:border-white rounded-2xl flex items-center justify-center">
                    <Monitor className="w-10 h-10 text-black dark:text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-black dark:text-white">
                    Windows
                  </CardTitle>
                  <CardDescription className="text-base text-black dark:text-white">
                    Coming Soon
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center space-y-4 pb-8">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-black dark:text-white">
                      Windows 10/11
                    </p>
                    <p className="text-sm text-black dark:text-white">
                      64-bit version
                    </p>
                  </div>
                  <Button disabled size="lg" className="w-full bg-white dark:bg-black text-black dark:text-white border border-black dark:border-white">
                    <Clock className="w-4 h-4 mr-2" />
                    Coming Soon
                  </Button>
                </CardContent>
              </Card>

              {/* Linux Download - Coming Soon */}
              <Card className="relative border border-black dark:border-white opacity-30">
                <CardHeader className="text-center pt-8">
                  <div className="w-20 h-20 mx-auto mb-4 border border-black dark:border-white rounded-2xl flex items-center justify-center">
                    <Laptop className="w-10 h-10 text-black dark:text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-black dark:text-white">
                    Linux
                  </CardTitle>
                  <CardDescription className="text-base text-black dark:text-white">
                    Coming Soon
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center space-y-4 pb-8">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-black dark:text-white">
                      Ubuntu, Debian, Fedora
                    </p>
                    <p className="text-sm text-black dark:text-white">
                      AppImage, .deb, .rpm
                    </p>
                  </div>
                  <Button disabled size="lg" className="w-full bg-white dark:bg-black text-black dark:text-white border border-black dark:border-white">
                    <Clock className="w-4 h-4 mr-2" />
                    Coming Soon
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="mt-16 text-center">
              <p className="text-sm text-black dark:text-white opacity-70">
                Sidekick is currently in beta. More platforms coming soon.
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
