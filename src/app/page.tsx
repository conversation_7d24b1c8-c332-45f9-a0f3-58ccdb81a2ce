"use client";

import { useState } from "react";
import { CustomBackgroundPaths } from "@/components/ui/background-paths-custom";
import { ShapeLandingHero } from "@/components/ui/shape-landing-hero";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Footer } from "@/components/ui/footer";
import { Button } from "@/components/ui/button";
import { Shapes, Waves } from "lucide-react";

export default function Home() {
  const [activeComponent, setActiveComponent] = useState<'paths' | 'shapes'>('paths');

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-950 flex flex-col">
      {/* Component Toggle Controls */}
      <div className="absolute top-4 left-4 z-30 flex gap-2">
        <Button
          variant={activeComponent === 'paths' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setActiveComponent('paths')}
          className="gap-2"
        >
          <Waves className="h-4 w-4" />
          Background Paths
        </Button>
        <Button
          variant={activeComponent === 'shapes' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setActiveComponent('shapes')}
          className="gap-2"
        >
          <Shapes className="h-4 w-4" />
          Shape Hero
        </Button>
      </div>

      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-30">
        <ThemeToggle />
      </div>

      {/* Render Active Component */}
      {activeComponent === 'paths' ? (
        <CustomBackgroundPaths title="Sidekick" />
      ) : (
        <ShapeLandingHero
          title="Shape Hero"
          subtitle="Experience beautiful animated shapes and modern design patterns that bring your interface to life."
        />
      )}

      <Footer />
    </div>
  );
}
